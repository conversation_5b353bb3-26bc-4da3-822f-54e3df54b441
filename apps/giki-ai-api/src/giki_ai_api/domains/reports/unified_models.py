"""
Unified Reports Models
=====================

Consolidated reports models using the unified base model system.
This demonstrates consolidation of reporting and analytics patterns.

Replaces patterns from:
- domains/reports/models.py
- domains/reports/schemas.py
- Common reporting validation patterns

Key improvements:
- Uses CRUDModel for common fields
- Uses ConfigurableModel for report configuration
- Uses MetricsModel for analytics data
- Standardized response schemas for reporting data
"""

from datetime import date, datetime
from typing import Any, Dict, List, Optional

from pydantic import Field

from ...shared.models import (
    BaseModel,
    ConfigurableModel,
    CreateSchema,
    CRUDModel,
    ListResponseSchema,
    MetricsModel,
    ResponseSchema,
    UpdateSchema,
)


class CustomReport(CRUDModel, ConfigurableModel):
    """
    Unified custom report model with configuration management.
    
    Consolidates common patterns:
    - CRUDModel: id, tenant_id, user_id, created_at, updated_at
    - ConfigurableModel: configuration management and validation
    """
    
    # Report metadata
    name: str = Field(..., max_length=255, description="Report name")
    description: Optional[str] = Field(None, description="Report description")
    report_type: str = Field(..., max_length=100, description="Type of report")
    
    # Report configuration (inherited from ConfigurableModel)
    # configuration: Dict[str, Any] - provided by ConfigurableModel
    
    # Report status and usage
    is_active: bool = Field(default=True, description="Is report active")
    is_public: bool = Field(default=False, description="Is report shared publicly")
    usage_count: int = Field(default=0, ge=0, description="Number of times report was run")
    last_run_at: Optional[datetime] = Field(None, description="When report was last executed")
    
    # Report scheduling
    is_scheduled: bool = Field(default=False, description="Is report scheduled")
    schedule_config: Optional[Dict[str, Any]] = Field(None, description="Schedule configuration")
    
    @property
    def is_frequently_used(self) -> bool:
        """Check if report is frequently used."""
        return self.usage_count >= 10
    
    @property
    def report_category(self) -> str:
        """Get report category from type."""
        type_mapping = {
            "spending_by_category": "financial",
            "spending_by_entity": "financial", 
            "transaction_summary": "financial",
            "categorization_accuracy": "analytics",
            "processing_metrics": "analytics",
            "custom_query": "custom"
        }
        return type_mapping.get(self.report_type, "other")


class ReportExecution(CRUDModel, MetricsModel):
    """
    Unified report execution tracking model.
    
    Consolidates common patterns:
    - CRUDModel: id, tenant_id, user_id, created_at, updated_at
    - MetricsModel: execution metrics and performance tracking
    """
    
    # Report reference
    report_id: int = Field(..., description="Custom report ID")
    report_name: str = Field(..., max_length=255, description="Report name at execution time")
    
    # Execution parameters
    execution_parameters: Optional[Dict[str, Any]] = Field(None, description="Parameters used for execution")
    date_range_start: Optional[date] = Field(None, description="Report date range start")
    date_range_end: Optional[date] = Field(None, description="Report date range end")
    
    # Execution status
    execution_status: str = Field(default="pending", description="Status: pending, running, completed, failed")
    started_at: Optional[datetime] = Field(None, description="Execution start time")
    completed_at: Optional[datetime] = Field(None, description="Execution completion time")
    
    # Results metadata
    result_count: int = Field(default=0, ge=0, description="Number of result records")
    result_size_bytes: Optional[int] = Field(None, ge=0, description="Size of result data")
    export_format: Optional[str] = Field(None, max_length=20, description="Export format: csv, excel, pdf")
    
    # Performance metrics (inherited from MetricsModel)
    # processing_time_ms, memory_usage_mb, etc.
    
    # Error handling
    error_message: Optional[str] = Field(None, description="Error message if execution failed")
    error_details: Optional[Dict[str, Any]] = Field(None, description="Detailed error information")
    
    @property
    def execution_duration_seconds(self) -> Optional[float]:
        """Calculate execution duration in seconds."""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None
    
    @property
    def is_successful(self) -> bool:
        """Check if execution was successful."""
        return self.execution_status == "completed" and not self.error_message
    
    @property
    def performance_category(self) -> str:
        """Categorize execution performance."""
        if not self.processing_time_ms:
            return "unknown"
        elif self.processing_time_ms < 1000:
            return "fast"
        elif self.processing_time_ms < 5000:
            return "moderate"
        else:
            return "slow"


# Reporting Data Models

class SpendingByCategoryItem(BaseModel):
    """Aggregated spending data for a single category."""
    
    category_path: str = Field(..., description="Full path of the category")
    category_id: Optional[int] = Field(None, description="Category ID")
    total_amount: float = Field(..., description="Total amount spent in this category")
    transaction_count: int = Field(..., ge=0, description="Number of transactions in this category")
    average_amount: Optional[float] = Field(None, description="Average transaction amount")
    
    # Time-based metrics
    first_transaction_date: Optional[date] = Field(None, description="Date of first transaction")
    last_transaction_date: Optional[date] = Field(None, description="Date of last transaction")
    
    @property
    def spending_frequency(self) -> str:
        """Categorize spending frequency."""
        if self.transaction_count >= 20:
            return "high"
        elif self.transaction_count >= 5:
            return "medium"
        else:
            return "low"


class EntitySpendingItem(BaseModel):
    """Aggregated spending data for a single entity/vendor."""
    
    entity_name: str = Field(..., description="Name of the entity/vendor")
    entity_id: Optional[int] = Field(None, description="Entity ID if available")
    total_amount: float = Field(..., description="Total amount spent with this entity")
    transaction_count: int = Field(..., ge=0, description="Number of transactions with this entity")
    average_amount: Optional[float] = Field(None, description="Average transaction amount")
    
    # Entity categorization
    primary_category: Optional[str] = Field(None, description="Most common category for this entity")
    category_diversity: Optional[int] = Field(None, ge=0, description="Number of different categories")
    
    @property
    def relationship_strength(self) -> str:
        """Categorize relationship strength with entity."""
        if self.transaction_count >= 10:
            return "strong"
        elif self.transaction_count >= 3:
            return "moderate"
        else:
            return "weak"


# CRUD Schemas using unified base schemas

class CustomReportCreate(CreateSchema):
    """Schema for creating a new custom report."""
    
    name: str = Field(..., max_length=255, description="Report name")
    description: Optional[str] = Field(None, description="Report description")
    report_type: str = Field(..., max_length=100, description="Type of report")
    configuration: Dict[str, Any] = Field(default_factory=dict, description="Report configuration")
    is_public: bool = Field(default=False, description="Is report shared publicly")


class CustomReportUpdate(UpdateSchema):
    """Schema for updating an existing custom report."""
    
    name: Optional[str] = Field(None, max_length=255, description="Report name")
    description: Optional[str] = Field(None, description="Report description")
    configuration: Optional[Dict[str, Any]] = Field(None, description="Report configuration")
    is_active: Optional[bool] = Field(None, description="Is report active")
    is_public: Optional[bool] = Field(None, description="Is report shared publicly")


class CustomReportResponse(ResponseSchema, CustomReport):
    """Schema for custom report API responses."""
    
    # Additional computed fields for responses
    execution_count: Optional[int] = Field(None, ge=0, description="Number of times executed")
    last_execution_status: Optional[str] = Field(None, description="Status of last execution")


class CustomReportListResponse(ListResponseSchema):
    """Schema for paginated custom report list responses."""
    
    reports: List[CustomReportResponse] = Field(..., description="List of custom reports")
    
    # Summary statistics
    active_reports: int = Field(default=0, ge=0, description="Number of active reports")
    public_reports: int = Field(default=0, ge=0, description="Number of public reports")
    scheduled_reports: int = Field(default=0, ge=0, description="Number of scheduled reports")


class SpendingByCategoryResponse(BaseModel):
    """Response model for spending by category report."""
    
    items: List[SpendingByCategoryItem] = Field(..., description="Spending data by category")
    total_records: int = Field(..., ge=0, description="Total number of categories with spending")
    total_amount: float = Field(..., description="Total amount across all categories")
    date_range_start: Optional[date] = Field(None, description="Report date range start")
    date_range_end: Optional[date] = Field(None, description="Report date range end")
    
    @property
    def average_category_spending(self) -> float:
        """Calculate average spending per category."""
        return self.total_amount / self.total_records if self.total_records > 0 else 0.0


class EntitySpendingResponse(BaseModel):
    """Response model for spending by entity report."""
    
    items: List[EntitySpendingItem] = Field(..., description="Spending data by entity")
    total_records: int = Field(..., ge=0, description="Total number of entities with spending")
    total_amount: float = Field(..., description="Total amount across all entities")
    date_range_start: Optional[date] = Field(None, description="Report date range start")
    date_range_end: Optional[date] = Field(None, description="Report date range end")
    
    @property
    def average_entity_spending(self) -> float:
        """Calculate average spending per entity."""
        return self.total_amount / self.total_records if self.total_records > 0 else 0.0


# Export all unified models
__all__ = [
    # Core models
    "CustomReport",
    "ReportExecution",
    
    # Data models
    "SpendingByCategoryItem",
    "EntitySpendingItem",
    
    # CRUD schemas
    "CustomReportCreate",
    "CustomReportUpdate",
    "CustomReportResponse", 
    "CustomReportListResponse",
    
    # Response schemas
    "SpendingByCategoryResponse",
    "EntitySpendingResponse",
]
